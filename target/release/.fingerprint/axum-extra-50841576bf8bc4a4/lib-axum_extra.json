{"rustc": 15597765236515928571, "features": "[\"default\", \"multipart\", \"tracing\"]", "declared_features": "[\"__private_docs\", \"async-read-body\", \"async-stream\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"error-response\", \"file-stream\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"scheme\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 11385615876995111466, "path": 16265052738622588318, "deps": [[784494742817713399, "tower_service", false, 14642258195595399745], [1906322745568073236, "pin_project_lite", false, 4326829896956888629], [5695049318159433696, "tower", false, 8635290888733428048], [7712452662827335977, "tower_layer", false, 16690259833872278162], [7858942147296547339, "rustversion", false, 9381389543823993197], [9010263965687315507, "http", false, 6034707596149516503], [9689903380558560274, "serde", false, 1303220784359019280], [10229185211513642314, "mime", false, 5517591731061880959], [10629569228670356391, "futures_util", false, 8549032822826012693], [12285238697122577036, "fastrand", false, 17960461255235380472], [12757619235593077227, "multer", false, 15283553201521416832], [13071899967998615733, "axum", false, 1936985416041827709], [14084095096285906100, "http_body", false, 16263387414793429899], [15176407853393882315, "axum_core", false, ********86044893007], [16066129441945555748, "bytes", false, 4225655364691431490], [16900715236047033623, "http_body_util", false, 16192245281839817827]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-extra-50841576bf8bc4a4/dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}